import {
  COLLECTION_CLOUD,
  COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION,
  COLLECTION_SSL,
} from '../constants/collections.js';
import { THIRTY_DAYS_MILLISECOND, TTL_MILLISECOND } from '../constants/constants.js';
import { saveDoc } from '../providers/firestore.js';
import { encrypt, hash, pubEncrypt } from '../services/cryptography.js';

const DUMMY_FQDN = 'example.gmo.jp';
const DUMMY_IP = '127.0.0.1';

const SSL_DUMMY = {
  NOT_FREE_1_EXPIRED: {
    fqdn: DUMMY_FQDN,
    createdAt: '2025-01-01T00:00:00.000Z',
    expiredAt: '2024-12-31T23:59:59.000Z',
    result: [
      {
        fqdn: DUMMY_FQDN,
        validity: {
          not_before: '2024-04-23 07:22:50 +0000 UTC',
          not_after: '2025-01-05 07:22:49 +0000 UTC',
        },
        free_ssl_provider: null,
        hostname_verification: { verified: true },
        cert_verification: { verified: true },
        ip: DUMMY_IP,
      },
      {
        fqdn: DUMMY_FQDN,
        validity: {
          not_before: '2024-04-23 07:22:50 +0000 UTC',
          not_after: '2025-01-05 07:22:49 +0000 UTC',
        },
        free_ssl_provider: null,
        hostname_verification: { verified: true },
        cert_verification: { verified: true },
        ip: DUMMY_IP,
      },
    ],
    type: 'NOT_FREE',
  },
  NOT_FREE_1_VALID: {
    fqdn: DUMMY_FQDN,
    createdAt: '2025-01-01T00:00:00.000Z',
    expiredAt: '2999-12-31T23:59:59.000Z',
    result: [
      {
        fqdn: DUMMY_FQDN,
        ip: DUMMY_IP,
        port: 443,
        hostname_verification: { verified: true },
        cert_verification: { verified: true },
        free_ssl_provider: null,
        validity: {
          not_before: '2024-01-01T00:00:00Z',
          not_after: '2025-12-31T23:59:59Z',
        },
      },
    ],
    type: 'NOT_FREE',
  },
  NOT_FREE_2_EXPIRED: {
    fqdn: DUMMY_FQDN,
    createdAt: '2025-01-01T00:00:00.000Z',
    expiredAt: '2024-12-31T23:59:59.000Z',
    result: [
      {
        fqdn: DUMMY_FQDN,
        validity: {
          not_before: '2024-04-23 07:22:50 +0000 UTC',
          not_after: '2025-04-05 07:22:49 +0000 UTC',
        },
        free_ssl_provider: null,
        hostname_verification: { verified: true },
        cert_verification: { verified: true },
        ip: DUMMY_IP,
      },
      {
        fqdn: DUMMY_FQDN,
        validity: {
          not_before: '2024-04-23 07:22:50 +0000 UTC',
          not_after: '2025-04-05 07:22:49 +0000 UTC',
        },
        free_ssl_provider: null,
        hostname_verification: { verified: true },
        cert_verification: { verified: true },
        ip: DUMMY_IP,
      },
    ],
    type: 'NOT_FREE',
  },
  NOT_FREE_2_VALID: {
    fqdn: DUMMY_FQDN,
    createdAt: '2025-01-01T00:00:00.000Z',
    expiredAt: '2999-12-31T23:59:59.000Z',
    result: [
      {
        fqdn: DUMMY_FQDN,
        ip: DUMMY_IP,
        port: 443,
        hostname_verification: { verified: false },
        cert_verification: { verified: true },
        free_ssl_provider: null,
        validity: {
          not_before: '2024-01-01T00:00:00Z',
          not_after: '2025-12-31T23:59:59Z',
        },
      },
    ],
    type: 'NOT_FREE',
  },
  NOT_FREE_3_EXPIRED: {
    fqdn: DUMMY_FQDN,
    createdAt: '2025-01-01T00:00:00.000Z',
    expiredAt: '2024-12-31T23:59:59.000Z',
    result: [
      {
        fqdn: DUMMY_FQDN,
        validity: {
          not_before: '2024-04-23 07:22:50 +0000 UTC',
          not_after: '2025-07-25 07:22:49 +0000 UTC',
        },
        free_ssl_provider: null,
        hostname_verification: { verified: true },
        cert_verification: { verified: true },
        ip: DUMMY_IP,
      },
      {
        fqdn: DUMMY_FQDN,
        validity: {
          not_before: '2024-04-23 07:22:50 +0000 UTC',
          not_after: '2025-07-25 07:22:49 +0000 UTC',
        },
        free_ssl_provider: null,
        hostname_verification: { verified: true },
        cert_verification: { verified: true },
        ip: DUMMY_IP,
      },
    ],
    type: 'NOT_FREE',
  },
  NOT_FREE_3_VALID: {
    fqdn: DUMMY_FQDN,
    createdAt: '2025-01-01T00:00:00.000Z',
    expiredAt: '2999-12-31T23:59:59.000Z',
    result: [
      {
        fqdn: DUMMY_FQDN,
        ip: DUMMY_IP,
        port: 443,
        hostname_verification: { verified: true },
        cert_verification: { verified: false },
        free_ssl_provider: null,
        validity: {
          not_before: '2024-01-01T00:00:00Z',
          not_after: '2025-12-31T23:59:59Z',
        },
      },
    ],
    type: 'NOT_FREE',
  },
  FREE_1_EXPIRED: {
    fqdn: DUMMY_FQDN,
    createdAt: '2025-01-01T00:00:00.000Z',
    expiredAt: '2024-12-31T23:59:59.000Z',
    result: [
      {
        fqdn: DUMMY_FQDN,
        validity: {
          not_before: '2024-04-23 07:22:50 +0000 UTC',
          not_after: '2025-05-15 13:55:59 +0000 UTC',
        },
        free_ssl_provider: "Let's Encrypt",
        hostname_verification: { verified: true },
        cert_verification: { verified: true },
        ip: DUMMY_IP,
      },
    ],
    type: "Let's Encrypt 2月",
  },
  FREE_2: {
    fqdn: DUMMY_FQDN,
    createdAt: '2021-01-01T00:00:00.000Z',
    expiredAt: '2021-01-01T00:00:00.000Z',
    result: [
      {
        fqdn: DUMMY_FQDN,
        validity: {
          not_before: '2024-04-23 07:22:50 +0000 UTC',
          not_after: '2025-04-05 13:55:59 +0000 UTC',
        },
        free_ssl_provider: "Let's Encrypt",
        hostname_verification: { verified: true },
        cert_verification: { verified: true },
        ip: DUMMY_IP,
      },
    ],
    type: "Let's Encrypt　15日",
  },
  FREE_3: {
    fqdn: DUMMY_FQDN,
    createdAt: '2021-01-01T00:00:00.000Z',
    expiredAt: '2021-01-01T00:00:00.000Z',
    result: [
      {
        fqdn: DUMMY_FQDN,
        validity: {
          not_before: '2024-04-23 07:22:50 +0000 UTC',
          not_after: '2024-12-15 13:55:59 +0000 UTC',
        },
        free_ssl_provider: "Let's Encrypt",
        hostname_verification: { verified: true },
        cert_verification: { verified: true },
        ip: DUMMY_IP,
      },
    ],
    type: "Let's Encrypt　期限切れ",
  },
  ERROR_1: {
    fqdn: DUMMY_FQDN,
    createdAt: '2021-01-01T00:00:00.000Z',
    expiredAt: '2021-01-01T00:00:00.000Z',
    result: [
      {
        fqdn: DUMMY_FQDN,
        validity: {
          not_before: '2024-04-23 07:22:50 +0000 UTC',
          not_after: '2024-12-15 13:55:59 +0000 UTC',
        },
        free_ssl_provider: "Let's Encrypt",
        hostname_verification: { verified: false },
        cert_verification: { verified: true },
        ip: DUMMY_IP,
      },
    ],
    type: 'hostname error',
  },
  ERROR_2: {
    fqdn: DUMMY_FQDN,
    createdAt: '2021-01-01T00:00:00.000Z',
    expiredAt: '2021-01-01T00:00:00.000Z',
    result: [
      {
        fqdn: DUMMY_FQDN,
        validity: {
          not_before: '2024-04-23 07:22:50 +0000 UTC',
          not_after: '2024-12-15 13:55:59 +0000 UTC',
        },
        free_ssl_provider: "Let's Encrypt",
        hostname_verification: { verified: true },
        cert_verification: { verified: false },
        ip: DUMMY_IP,
      },
    ],
    type: 'cert error',
  },
};

const DUMMY_CLOUD = {
  AWS_EXPIRED: {
    fqdn: DUMMY_FQDN,
    createdAt: '2025-01-30T09:28:35.770Z',
    expiredAt: '2024-12-31T23:59:59.000Z',
    result: [
      {
        provider: 'AWS',
        service: 'AMAZON',
        region: 'GLOBAL',
        ip: DUMMY_IP,
      },
    ],
  },
  AWS_VALID: {
    fqdn: DUMMY_FQDN,
    createdAt: '2025-01-30T09:28:35.770Z',
    expiredAt: '2999-12-31T23:59:59.000Z',
    result: [
      {
        provider: 'AWS',
        service: 'AMAZON',
        region: 'GLOBAL',
        ip: DUMMY_IP,
      },
    ],
  },
  GCP_EXPIRED: {
    fqdn: DUMMY_FQDN,
    createdAt: '2025-01-30T09:28:35.770Z',
    expiredAt: '2024-12-31T23:59:59.000Z',
    result: [
      {
        provider: 'GoogleCloud',
        service: 'GOOGLE',
        region: 'GLOBAL',
        ip: DUMMY_IP,
      },
    ],
  },
  GCP_VALID: {
    fqdn: DUMMY_FQDN,
    createdAt: '2025-01-30T09:28:35.770Z',
    expiredAt: '2999-12-31T23:59:59.000Z',
    result: [
      {
        provider: 'GoogleCloud',
        service: 'Google',
        region: 'GLOBAL',
        ip: DUMMY_IP,
      },
    ],
  },
  AZURE_EXPIRED: {
    fqdn: DUMMY_FQDN,
    createdAt: '2025-01-30T09:28:35.770Z',
    expiredAt: '2024-12-31T23:59:59.000Z',
    result: [
      {
        provider: 'Azure',
        service: 'MICROSOFT',
        region: 'GLOBAL',
        ip: DUMMY_IP,
      },
    ],
  },
  AZURE_VALID: {
    fqdn: DUMMY_FQDN,
    createdAt: '2025-01-30T09:28:35.770Z',
    expiredAt: '2999-12-31T23:59:59.000Z',
    result: [
      {
        provider: 'Azure',
        service: 'Microsoft',
        region: 'GLOBAL',
        ip: DUMMY_IP,
      },
    ],
  },
  NO_CLOUD_EXPIRED: {
    fqdn: DUMMY_FQDN,
    createdAt: '2025-01-30T09:28:35.770Z',
    expiredAt: '2024-12-31T23:59:59.000Z',
    result: [],
  },
  NO_CLOUD_VALID: {
    fqdn: DUMMY_FQDN,
    createdAt: '2025-01-30T09:28:35.770Z',
    expiredAt: '2999-12-31T23:59:59.000Z',
    result: [],
  },
  CLOUD_ERROR_EXPIRED: {
    fqdn: DUMMY_FQDN,
    createdAt: '2025-01-30T09:28:35.770Z',
    expiredAt: '2024-12-31T23:59:59.000Z',
    result: [],
    status: 'error',
  },
  CLOUD_ERROR_VALID: {
    fqdn: DUMMY_FQDN,
    createdAt: '2025-01-30T09:28:35.770Z',
    expiredAt: '2999-12-31T23:59:59.000Z',
    result: [],
    status: 'error',
  },
};

const RANK = ['A', 'B', 'C', 'D'];
const SPF = ['undefined', true, false];
const DMARC = ['undefined', null, { policy: 'none' }, { policy: 'quarantine' }, { policy: 'reject' }];
const DUMMY_IMPERSONATION = (RANK, SPF, DMARC, isExpired = false) => {
  const expiredAt = isExpired ? '2024-12-31T23:59:59.000Z' : '2999-12-31T23:59:59.000Z';
  let data = {
    fqdn: DUMMY_FQDN,
    createdAt: '2025-01-30T09:28:35.770Z',
    expiredAt,
    result: {
      brand_tld: false,
      bimi: true,
      rank: RANK,
      score: 0,
      vmc: {
        domain_nm: DUMMY_FQDN,
        version: '',
        image_url: '',
        issuer: '',
        subject: '',
        not_before: '0001-01-01T00:00:00Z',
        not_after: '0001-01-01T00:00:00Z',
        serial_number: null,
        DNSNames: null,
        email_addresses: null,
        raw_data: null,
        inp_user: '',
        inp_date: '',
        upd_user: '',
        upd_date: '',
      },
      czds: {
        count: 0,
        results: [],
      },
      czds_key: 'example.gmo',
    },
  };

  if (SPF !== 'undefined') {
    data.result.spf = SPF;
  }
  if (DMARC !== 'undefined') {
    data.result.dmarc = DMARC;
  }

  return data;
};

const generateVulnerabilityItems = (rank) => {
  const baseItems = [
    {
      category: 'host',
      cvss: 0,
      description: 'ICMP TimeStampクエリに対してサーバが応答しています。',
      impact: 'この機能はサーバーの時刻を表示するだけで、直接的な脆弱性ではありません。',
      measure: 'ICMP Timestampクエリに応答しない設定を推奨します。',
      risk_level: 'info',
      scan_type: 'passive',
      title: 'ICMP TimeStamp クエリへ応答',
    },
    {
      category: 'ssl/tls',
      cvss: 1,
      description: 'OCSP Staplingが無効です。',
      impact: 'OCSPのレスポンスが得られない場合、サーバ証明書の失効を検証せずにSSL通信を許可してしまう恐れがあります。',
      measure: 'OCSP Staplingの有効化を推奨します。',
      risk_level: 'info',
      scan_type: 'active',
      title: 'OCSP Stapling が有効になっていないサーバ',
    },
    {
      category: 'service',
      cvss: 3,
      description: 'Webサーバのレスポンスヘッダでは「HTTP Strict Transport Security（HSTS）」が未設定です。',
      impact: 'Webサイトが中間者攻撃やセッションハイジャッキングに対して脆弱である可能性があります。',
      measure: 'HSTS（HTTP Strict Transport Security）の有効化を推奨します。',
      risk_level: 'low',
      scan_type: 'passive',
      title: 'セキュリティヘッダ "HTTP Strict Transport Security (HSTS)" の未設定',
    },
  ];

  if (rank === 'A') {
    return baseItems.slice(0, 1);
  } else if (rank === 'B') {
    return baseItems.slice(0, 2);
  } else if (rank === 'C') {
    return baseItems;
  } else if (rank === 'D') {
    return [
      ...baseItems,
      {
        category: 'service',
        cvss: 5.5,
        description: 'SQLインジェクションの脆弱性が検出されました。',
        impact: 'データベースの不正操作や情報漏洩のリスクがあります。',
        measure: 'パラメータ化クエリの使用を推奨します。',
        risk_level: 'medium',
        scan_type: 'active',
        title: 'SQLインジェクション脆弱性',
      },
    ];
  } else if (rank === 'E') {
    return [
      ...baseItems,
      {
        category: 'service',
        cvss: 5.5,
        description: 'SQLインジェクションの脆弱性が検出されました。',
        impact: 'データベースの不正操作や情報漏洩のリスクがあります。',
        measure: 'パラメータ化クエリの使用を推奨します。',
        risk_level: 'medium',
        scan_type: 'active',
        title: 'SQLインジェクション脆弱性',
      },
      {
        category: 'service',
        cvss: 8.5,
        description: 'リモートコード実行の脆弱性が検出されました。',
        impact: 'システムの完全な制御を奪われるリスクがあります。',
        measure: '緊急でパッチの適用を推奨します。',
        risk_level: 'high',
        scan_type: 'active',
        title: 'リモートコード実行脆弱性',
      },
      {
        category: 'service',
        cvss: 9.8,
        description: '認証バイパスの脆弱性が検出されました。',
        impact: '管理者権限の不正取得のリスクがあります。',
        measure: '即座にシステムの停止とパッチ適用を推奨します。',
        risk_level: 'critical',
        scan_type: 'active',
        title: '認証バイパス脆弱性',
      },
    ];
  }
  return baseItems;
};

const DUMMY_NDS = (RANK, isExpired = false) => {
  const expiredAt = isExpired ? '2024-12-31T23:59:59.000Z' : '2999-12-31T23:59:59.000Z';
  const items = generateVulnerabilityItems(RANK);

  return {
    fqdn: DUMMY_FQDN,
    createdAt: '2025-01-30T09:28:35.770Z',
    expiredAt,
    result: {
      id: '8265',
      fqdn: DUMMY_FQDN,
      scan_mode: 'all',
      scan_risk_level: 'info',
      assessment_time: '2025-01-10T19:04:38+0900',
      scan_by: 'manual',
      status: 'completed',
      rank: RANK,
      result: {
        items: [
          {
            category: 'host',
            cvss: 0,
            description:
              'ICMP TimeStampクエリに対してサーバが応答しています。\nICMP TimeStampの応答により、サーバの稼働時間やタイムスタンプ情報が取得される可能性があり、攻撃者がこれらの情報を悪用してネットワークの調査や攻撃タイミングを計画するリスクがあります。\nそのため、ICMP TimeStampクエリへの応答を無効化することを検討してください。',
            impact:
              'この機能はサーバーの時刻を表示するだけで、直接的な脆弱性ではありません。\nただし、暗号化方式によってはサーバーの現在時刻を基に暗号化を行う手法も存在し、時刻情報が暗号解読の一因となる恐れがあります。',
            measure:
              'ICMP Timestampクエリに応答しない設定を推奨します。\n\n1. サーバの設定ファイルやファイアウォール設定を確認する\n2. ICMP Timestampクエリへの応答を無効にする設定を追加する（例：ファイアウォールのルールでICMPタイプ13のパケットをブロックする）\n3. 設定を保存し、サーバやネットワーク機器を再起動する\n4. ICMP Timestampクエリに対して応答しないことを確認する（適切なツールを使ってテスト）',
            risk_level: 'info',
            scan_type: 'passive',
            title: 'ICMP TimeStamp クエリへ応答',
          },
          {
            category: 'ssl/tls',
            cvss: 1,
            description:
              'OCSP Staplingが無効です。\nOCSPはサーバ証明書の失効を確認する手段の一つですが、OCSPレスポンスが得られない場合に、サーバ証明書の失効を検証せずにSSL通信を許可したり、ブラウザのアクセス履歴がOCSPレスポンダに開示されるなどの問題があります。\nこれらの問題を解決するためには、サーバがOCSPレスポンダから取得したOCSPレコードをキャッシュし、SSL/TLSハンドシェイク中にクライアントに送信するOCSP Stapling機能を有効化することが推奨されます。\nそのため、OCSP Staplingを有効にして、通信の安全性を高めてください。',
            impact:
              'OCSPのレスポンスが得られない場合、サーバ証明書の失効を検証せずにSSL通信を許可してしまう恐れがあります。\nまた、ブラウザのアクセス履歴がOCSPレスポンダに開示される可能性もあります。\nこれにより、セキュリティリスクが高まり、不正な証明書が使用されるリスクやプライバシー侵害が発生する可能性があります。',
            measure:
              'OCSP Staplingの有効化を推奨します。\n\n1. Webサーバの設定ファイルを確認し、OCSP Staplingを有効化する設定を追加する（例：Apacheでは「SSLUseStapling On」、Nginxでは「ssl_stapling on」）\n2. OCSPレスポンダーのURLを適切に設定し、証明書の有効性確認が行われるように設定する\n3. 設定を保存し、Webサーバを再起動する\n4. OCSP Staplingが正しく動作していることを確認する（テストツールやブラウザを使用して確認）',
            risk_level: 'info',
            scan_type: 'active',
            title: 'OCSP Stapling が有効になっていないサーバ',
          },
          {
            category: 'service',
            cvss: 2,
            description:
              '診断対象ドメインに関連するサブドメイン一覧です。\nサブドメイン情報が攻撃者に知られると、サブドメインごとの脆弱性を狙った攻撃や調査の対象となるリスクがあります。\nそのため、サブドメインへのアクセス制限を強化し、不要なサブドメインの公開を避けることを推奨します。\n\n＜対象のサブドメイン一覧＞\n・img.gmo-toku.jp\n・mail.gmo-toku.jp\n・secure.gmo-toku.jp\n・www.gmo-toku.jp',
            impact:
              '攻撃者に有用な情報を提供してしまう恐れがあります。存在を認識していないドメインや、公開する意図がないにもかかわらずアクセス可能なドメインが存在する場合、それが攻撃の起点とされる可能性があります。\nまた、ドメインが登録されていても名前解決先のサーバが存在しない場合、サブドメインテイクオーバー攻撃の対象となるリスクがあります。',
            measure:
              '意図しない外部公開ドメインが存在する場合は、当該ドメインへの適切なアクセス制御を推奨します。\n\n1. 公開されているドメインを確認し、意図しない外部公開が行われているドメインを特定する\n2. ファイアウォールやDNS設定でアクセス制御を行い、許可されたIPアドレスや範囲のみがアクセスできるよう設定する\n3. 設定を保存し、アクセス制御が正しく機能していることを確認する\n4. 定期的にドメインのアクセスログを確認し、不正なアクセスや異常な接続がないかを監視する',
            risk_level: 'info',
            scan_type: 'passive',
            title: 'サブドメインの列挙',
          },
          {
            category: 'service',
            cvss: 3,
            description:
              'Webサーバのレスポンスヘッダでは「HTTP Strict Transport Security（HSTS）」が未設定です。\nHSTSは、常に安全な接続（HTTPS）を利用するようブラウザに指示するヘッダであり、サーバのTLS証明書が信頼されない場合には、ブラウザはアプリケーションとの接続を行わなくなります。また、ユーザがhttp://で始まるURLにアクセスした際には、自動的にhttps://に変更されます。\nHSTSを設定することで、中間者攻撃を防ぐことができるため、HSTSの設定を有効にすることを推奨します。\n\n＜対象のURL一覧＞\n・https://gmo-toku.jp/oadmin/',
            impact:
              'Webサイトが中間者攻撃やセッションハイジャッキングに対して脆弱である可能性があります。\nHTTPリクエスト時にHTTPSへのリダイレクトが設定されている場合でも、中間者攻撃によって、HTTPSへのアップグレード前に悪意あるサイトへ誘導される恐れがあります。',
            measure:
              'HSTS（HTTP Strict Transport Security）の有効化を推奨します。\n\n1. Webサーバの設定ファイルを確認し、HSTSヘッダを有効化する設定を追加する\n2. Apacheでは「Header always set Strict-Transport-Security ""max-age=31536000; includeSubDomains""」、Nginxでは「add_header Strict-Transport-Security ""max-age=31536000; includeSubDomains"";」などの設定を行う\n3. 設定を保存し、Webサーバを再起動する\n4. HSTSヘッダが正しく設定されていることをテストツールやブラウザで確認する\n5. 定期的に設定を見直し、HSTSが有効に機能していることを確認する',
            risk_level: 'low',
            scan_type: 'passive',
            title: 'セキュリティヘッダ "HTTP Strict Transport Security (HSTS)" の未設定',
          },
          {
            category: 'host',
            cvss: 4,
            description:
              'ICMP TimeStampクエリに対してサーバが応答しています。\nICMP TimeStampの応答により、サーバの稼働時間やタイムスタンプ情報が取得される可能性があり、攻撃者がこれらの情報を悪用してネットワークの調査や攻撃タイミングを計画するリスクがあります。\nそのため、ICMP TimeStampクエリへの応答を無効化することを検討してください。',
            impact:
              'この機能はサーバーの時刻を表示するだけで、直接的な脆弱性ではありません。\nただし、暗号化方式によってはサーバーの現在時刻を基に暗号化を行う手法も存在し、時刻情報が暗号解読の一因となる恐れがあります。',
            measure:
              'ICMP Timestampクエリに応答しない設定を推奨します。\n\n1. サーバの設定ファイルやファイアウォール設定を確認する\n2. ICMP Timestampクエリへの応答を無効にする設定を追加する（例：ファイアウォールのルールでICMPタイプ13のパケットをブロックする）\n3. 設定を保存し、サーバやネットワーク機器を再起動する\n4. ICMP Timestampクエリに対して応答しないことを確認する（適切なツールを使ってテスト）',
            risk_level: 'info',
            scan_type: 'passive',
            title: 'ICMP TimeStamp クエリへ応答',
          },
          {
            category: 'ssl/tls',
            cvss: 5,
            description:
              'OCSP Staplingが無効です。\nOCSPはサーバ証明書の失効を確認する手段の一つですが、OCSPレスポンスが得られない場合に、サーバ証明書の失効を検証せずにSSL通信を許可したり、ブラウザのアクセス履歴がOCSPレスポンダに開示されるなどの問題があります。\nこれらの問題を解決するためには、サーバがOCSPレスポンダから取得したOCSPレコードをキャッシュし、SSL/TLSハンドシェイク中にクライアントに送信するOCSP Stapling機能を有効化することが推奨されます。\nそのため、OCSP Staplingを有効にして、通信の安全性を高めてください。',
            impact:
              'OCSPのレスポンスが得られない場合、サーバ証明書の失効を検証せずにSSL通信を許可してしまう恐れがあります。\nまた、ブラウザのアクセス履歴がOCSPレスポンダに開示される可能性もあります。\nこれにより、セキュリティリスクが高まり、不正な証明書が使用されるリスクやプライバシー侵害が発生する可能性があります。',
            measure:
              'OCSP Staplingの有効化を推奨します。\n\n1. Webサーバの設定ファイルを確認し、OCSP Staplingを有効化する設定を追加する（例：Apacheでは「SSLUseStapling On」、Nginxでは「ssl_stapling on」）\n2. OCSPレスポンダーのURLを適切に設定し、証明書の有効性確認が行われるように設定する\n3. 設定を保存し、Webサーバを再起動する\n4. OCSP Staplingが正しく動作していることを確認する（テストツールやブラウザを使用して確認）',
            risk_level: 'info',
            scan_type: 'active',
            title: 'OCSP Stapling が有効になっていないサーバ',
          },
          {
            category: 'service',
            cvss: 6,
            description:
              '診断対象ドメインに関連するサブドメイン一覧です。\nサブドメイン情報が攻撃者に知られると、サブドメインごとの脆弱性を狙った攻撃や調査の対象となるリスクがあります。\nそのため、サブドメインへのアクセス制限を強化し、不要なサブドメインの公開を避けることを推奨します。\n\n＜対象のサブドメイン一覧＞\n・img.gmo-toku.jp\n・mail.gmo-toku.jp\n・secure.gmo-toku.jp\n・www.gmo-toku.jp',
            impact:
              '攻撃者に有用な情報を提供してしまう恐れがあります。存在を認識していないドメインや、公開する意図がないにもかかわらずアクセス可能なドメインが存在する場合、それが攻撃の起点とされる可能性があります。\nまた、ドメインが登録されていても名前解決先のサーバが存在しない場合、サブドメインテイクオーバー攻撃の対象となるリスクがあります。',
            measure:
              '意図しない外部公開ドメインが存在する場合は、当該ドメインへの適切なアクセス制御を推奨します。\n\n1. 公開されているドメインを確認し、意図しない外部公開が行われているドメインを特定する\n2. ファイアウォールやDNS設定でアクセス制御を行い、許可されたIPアドレスや範囲のみがアクセスできるよう設定する\n3. 設定を保存し、アクセス制御が正しく機能していることを確認する\n4. 定期的にドメインのアクセスログを確認し、不正なアクセスや異常な接続がないかを監視する',
            risk_level: 'info',
            scan_type: 'passive',
            title: 'サブドメインの列挙',
          },
          {
            category: 'service',
            cvss: 7,
            description:
              'Webサーバのレスポンスヘッダでは「HTTP Strict Transport Security（HSTS）」が未設定です。\nHSTSは、常に安全な接続（HTTPS）を利用するようブラウザに指示するヘッダであり、サーバのTLS証明書が信頼されない場合には、ブラウザはアプリケーションとの接続を行わなくなります。また、ユーザがhttp://で始まるURLにアクセスした際には、自動的にhttps://に変更されます。\nHSTSを設定することで、中間者攻撃を防ぐことができるため、HSTSの設定を有効にすることを推奨します。\n\n＜対象のURL一覧＞\n・https://gmo-toku.jp/oadmin/',
            impact:
              'Webサイトが中間者攻撃やセッションハイジャッキングに対して脆弱である可能性があります。\nHTTPリクエスト時にHTTPSへのリダイレクトが設定されている場合でも、中間者攻撃によって、HTTPSへのアップグレード前に悪意あるサイトへ誘導される恐れがあります。',
            measure:
              'HSTS（HTTP Strict Transport Security）の有効化を推奨します。\n\n1. Webサーバの設定ファイルを確認し、HSTSヘッダを有効化する設定を追加する\n2. Apacheでは「Header always set Strict-Transport-Security ""max-age=31536000; includeSubDomains""」、Nginxでは「add_header Strict-Transport-Security ""max-age=31536000; includeSubDomains"";」などの設定を行う\n3. 設定を保存し、Webサーバを再起動する\n4. HSTSヘッダが正しく設定されていることをテストツールやブラウザで確認する\n5. 定期的に設定を見直し、HSTSが有効に機能していることを確認する',
            risk_level: 'low',
            scan_type: 'passive',
            title: 'セキュリティヘッダ "HTTP Strict Transport Security (HSTS)" の未設定',
          },
          {
            category: 'ssl/tls',
            cvss: 8,
            description:
              'OCSP Staplingが無効です。\nOCSPはサーバ証明書の失効を確認する手段の一つですが、OCSPレスポンスが得られない場合に、サーバ証明書の失効を検証せずにSSL通信を許可したり、ブラウザのアクセス履歴がOCSPレスポンダに開示されるなどの問題があります。\nこれらの問題を解決するためには、サーバがOCSPレスポンダから取得したOCSPレコードをキャッシュし、SSL/TLSハンドシェイク中にクライアントに送信するOCSP Stapling機能を有効化することが推奨されます。\nそのため、OCSP Staplingを有効にして、通信の安全性を高めてください。',
            impact:
              'OCSPのレスポンスが得られない場合、サーバ証明書の失効を検証せずにSSL通信を許可してしまう恐れがあります。\nまた、ブラウザのアクセス履歴がOCSPレスポンダに開示される可能性もあります。\nこれにより、セキュリティリスクが高まり、不正な証明書が使用されるリスクやプライバシー侵害が発生する可能性があります。',
            measure:
              'OCSP Staplingの有効化を推奨します。\n\n1. Webサーバの設定ファイルを確認し、OCSP Staplingを有効化する設定を追加する（例：Apacheでは「SSLUseStapling On」、Nginxでは「ssl_stapling on」）\n2. OCSPレスポンダーのURLを適切に設定し、証明書の有効性確認が行われるように設定する\n3. 設定を保存し、Webサーバを再起動する\n4. OCSP Staplingが正しく動作していることを確認する（テストツールやブラウザを使用して確認）',
            risk_level: 'info',
            scan_type: 'active',
            title: 'OCSP Stapling が有効になっていないサーバ',
          },
          {
            category: 'service',
            cvss: 9,
            description:
              '診断対象ドメインに関連するサブドメイン一覧です。\nサブドメイン情報が攻撃者に知られると、サブドメインごとの脆弱性を狙った攻撃や調査の対象となるリスクがあります。\nそのため、サブドメインへのアクセス制限を強化し、不要なサブドメインの公開を避けることを推奨します。\n\n＜対象のサブドメイン一覧＞\n・img.gmo-toku.jp\n・mail.gmo-toku.jp\n・secure.gmo-toku.jp\n・www.gmo-toku.jp',
            impact:
              '攻撃者に有用な情報を提供してしまう恐れがあります。存在を認識していないドメインや、公開する意図がないにもかかわらずアクセス可能なドメインが存在する場合、それが攻撃の起点とされる可能性があります。\nまた、ドメインが登録されていても名前解決先のサーバが存在しない場合、サブドメインテイクオーバー攻撃の対象となるリスクがあります。',
            measure:
              '意図しない外部公開ドメインが存在する場合は、当該ドメインへの適切なアクセス制御を推奨します。\n\n1. 公開されているドメインを確認し、意図しない外部公開が行われているドメインを特定する\n2. ファイアウォールやDNS設定でアクセス制御を行い、許可されたIPアドレスや範囲のみがアクセスできるよう設定する\n3. 設定を保存し、アクセス制御が正しく機能していることを確認する\n4. 定期的にドメインのアクセスログを確認し、不正なアクセスや異常な接続がないかを監視する',
            risk_level: 'info',
            scan_type: 'passive',
            title: 'サブドメインの列挙',
          },
          {
            category: 'service',
            cvss: 10,
            description:
              'Webサーバのレスポンスヘッダでは「HTTP Strict Transport Security（HSTS）」が未設定です。\nHSTSは、常に安全な接続（HTTPS）を利用するようブラウザに指示するヘッダであり、サーバのTLS証明書が信頼されない場合には、ブラウザはアプリケーションとの接続を行わなくなります。また、ユーザがhttp://で始まるURLにアクセスした際には、自動的にhttps://に変更されます。\nHSTSを設定することで、中間者攻撃を防ぐことができるため、HSTSの設定を有効にすることを推奨します。\n\n＜対象のURL一覧＞\n・https://gmo-toku.jp/oadmin/',
            impact:
              'Webサイトが中間者攻撃やセッションハイジャッキングに対して脆弱である可能性があります。\nHTTPリクエスト時にHTTPSへのリダイレクトが設定されている場合でも、中間者攻撃によって、HTTPSへのアップグレード前に悪意あるサイトへ誘導される恐れがあります。',
            measure:
              'HSTS（HTTP Strict Transport Security）の有効化を推奨します。\n\n1. Webサーバの設定ファイルを確認し、HSTSヘッダを有効化する設定を追加する\n2. Apacheでは「Header always set Strict-Transport-Security ""max-age=31536000; includeSubDomains""」、Nginxでは「add_header Strict-Transport-Security ""max-age=31536000; includeSubDomains"";」などの設定を行う\n3. 設定を保存し、Webサーバを再起動する\n4. HSTSヘッダが正しく設定されていることをテストツールやブラウザで確認する\n5. 定期的に設定を見直し、HSTSが有効に機能していることを確認する',
            risk_level: 'low',
            scan_type: 'passive',
            title: 'セキュリティヘッダ "HTTP Strict Transport Security (HSTS)" の未設定',
          },
        ],
      },
    },
  };
};

const generateCode = async ({ email, fqdn, expiredAt }) => {
  const code = await encrypt(
    JSON.stringify({
      email,
      fqdn,
      expiredAt: expiredAt.getTime(),
    }),
    process.env.SECRET_CRYPTOGRAPHY_PASSWORD,
    process.env.SECRET_CRYPTOGRAPHY_SALT,
  );
  return code;
};

const addDummy = async (ssl, dataCloud, impersonation_rank, nds_rank, isExpired = false) => {
  const now = new Date();
  const expiredAt = isExpired ? new Date('2024-12-31T23:59:59.000Z') : new Date(now.getTime() + TTL_MILLISECOND);
  const email = createUniqueEmail();
  const fqdn = DUMMY_FQDN;
  const code = await generateCode({
    email,
    fqdn,
    expiredAt,
  });
  await saveDoc({ collection: COLLECTION_CLOUD, docId: code, data: dataCloud });
  await saveDoc({ collection: COLLECTION_SSL, docId: code, data: ssl });
  const config = await createSiteRiskConfig(email, fqdn);
  const siteRiskId = await hash(`${email}:${fqdn}`);
  await saveDoc({ collection: COLLECTION_REGULARLY_SITE_RISK_CONFIGURATION, docId: siteRiskId, data: config });

  const spf = SPF[Math.floor(Math.random() * SPF.length)];
  const dmarc = DMARC[Math.floor(Math.random() * DMARC.length)];

  await saveDoc({
    collection: 'impersonation',
    docId: code,
    data: DUMMY_IMPERSONATION(impersonation_rank, spf, dmarc, isExpired),
  });
  await saveDoc({
    collection: 'nds',
    docId: code,
    data: DUMMY_NDS(nds_rank, isExpired),
  });
  const { encryptedEmail, ...configuration } = config;
  console.log({
    ssl_type: ssl.type,
    cloud_provider: dataCloud.result[0]?.provider || 'None',
    impersonation_rank: impersonation_rank,
    nds_rank: nds_rank,
    spf: spf !== 'undefined' ? spf : null,
    dmarc: dmarc !== 'undefined' ? dmarc !== null : null,
    configuration,
    expired: isExpired,
    link: `${process.env.HOST}${process.env.PATH_PREFIX}/check/site-risk/?code=${code}`,
  });
};

const createUniqueEmail = () => {
  const uuid = crypto.randomUUID();
  return `example-${uuid}@gmo.jp`;
};

const createSiteRiskConfig = async (email, fqdn) => {
  const encryptedEmail = await pubEncrypt(email, process.env.SECRET_PUBLIC_KEY);

  let nextCheckedAt = new Date(Date.now() + THIRTY_DAYS_MILLISECOND);

  const intervalOptions = [1, 3, 6];
  const interval = intervalOptions[Math.floor(Math.random() * intervalOptions.length)];

  const isRegularyOptions = [true, false];
  const isRegularly = isRegularyOptions[Math.floor(Math.random() * isRegularyOptions.length)];

  if (!isRegularly) {
    nextCheckedAt = null;
  }

  const siteRisk = { encryptedEmail, fqdn, isRegularly, interval, nextCheckedAt: nextCheckedAt ? nextCheckedAt.toISOString() : null };
  return siteRisk;
};

const run = async () => {
  const ranks = ['A', 'B', 'C', 'D', 'E'];

  for (const sslKey in SSL_DUMMY) {
    const ssl = SSL_DUMMY[sslKey];
    for (const cloudKey in DUMMY_CLOUD) {
      const dataCloud = DUMMY_CLOUD[cloudKey];
      for (const rank of ranks) {
        const isExpired = sslKey.includes('EXPIRED') || cloudKey.includes('EXPIRED');
        await addDummy(ssl, dataCloud, rank, rank, isExpired);

        if (rank === 'D') {
          await addDummy(ssl, dataCloud, rank, 'E', isExpired);
        }
        if (rank === 'E') {
          await addDummy(ssl, dataCloud, 'D', rank, isExpired);
        }
      }
    }
  }
};

run();
